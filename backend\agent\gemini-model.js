import fetch from 'node-fetch';

// مفتاح API لـ Google Gemini
const GEMINI_API_KEY = process.env.GOOGLE_API_KEY;
const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';

// التحقق من وجود مفتاح API
if (!GEMINI_API_KEY) {
  console.warn('تحذير: مفتاح Google API غير موجود في متغيرات البيئة (GOOGLE_API_KEY)');
}

/**
 * دالة لإنشاء نموذج Gemini
 */
export function getGeminiModel() {
  return {
    // دالة لإرسال طلب إلى Gemini API
    call: async (prompt) => {
      try {
        // التحقق من وجود مفتاح API
        if (!GEMINI_API_KEY) {
          throw new Error('مفتاح Google API غير موجود. يرجى إضافة GOOGLE_API_KEY في ملف .env');
        }
        console.log(`Sending prompt to Gemini API: ${prompt.substring(0, 100)}...`);

        const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            contents: [
              {
                parts: [
                  {
                    text: prompt
                  }
                ]
              }
            ]
          })
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`Gemini API error: ${response.status} ${response.statusText} - ${errorText}`);
        }

        const data = await response.json();
        console.log('Gemini API response received');

        // استخراج النص من الاستجابة
        const text = data.candidates?.[0]?.content?.parts?.[0]?.text || '';
        return text;
      } catch (error) {
        console.error('Error calling Gemini API:', error);
        throw error;
      }
    }
  };
}

// نظام اكتشاف قاعدة البيانات التلقائي
import mysql from "mysql2/promise";

class DatabaseSchemaDiscovery {
  constructor(connectionConfig) {
    this.connectionConfig = connectionConfig;
    this.schema = null;
    this.relationships = [];
  }

  async discoverSchema() {
    const connection = await mysql.createConnection(this.connectionConfig);

    try {
      // اكتشاف جميع الجداول
      const tables = await this.getTables(connection);

      // اكتشاف هيكل كل جدول
      const tablesSchema = {};
      for (const table of tables) {
        tablesSchema[table] = await this.getTableSchema(connection, table);
      }

      // اكتشاف العلاقات بين الجداول
      this.relationships = await this.discoverRelationships(connection);

      this.schema = {
        database: this.connectionConfig.database,
        tables: tablesSchema,
        relationships: this.relationships
      };

      return this.schema;
    } finally {
      await connection.end();
    }
  }

  async getTables(connection) {
    const [rows] = await connection.execute(`
      SELECT TABLE_NAME
      FROM INFORMATION_SCHEMA.TABLES
      WHERE TABLE_SCHEMA = ?
    `, [this.connectionConfig.database]);

    return rows.map(row => row.TABLE_NAME);
  }

  async getTableSchema(connection, tableName) {
    const [columns] = await connection.execute(`
      SELECT
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE,
        COLUMN_KEY,
        COLUMN_DEFAULT,
        EXTRA,
        COLUMN_COMMENT
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?
      ORDER BY ORDINAL_POSITION
    `, [this.connectionConfig.database, tableName]);

    return {
      name: tableName,
      columns: columns.map(col => ({
        name: col.COLUMN_NAME,
        type: col.DATA_TYPE,
        nullable: col.IS_NULLABLE === 'YES',
        key: col.COLUMN_KEY,
        default: col.COLUMN_DEFAULT,
        extra: col.EXTRA,
        comment: col.COLUMN_COMMENT
      }))
    };
  }

  async discoverRelationships(connection) {
    const [relationships] = await connection.execute(`
      SELECT
        TABLE_NAME,
        COLUMN_NAME,
        CONSTRAINT_NAME,
        REFERENCED_TABLE_NAME,
        REFERENCED_COLUMN_NAME
      FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
      WHERE TABLE_SCHEMA = ?
        AND REFERENCED_TABLE_NAME IS NOT NULL
    `, [this.connectionConfig.database]);

    return relationships.map(rel => ({
      fromTable: rel.TABLE_NAME,
      fromColumn: rel.COLUMN_NAME,
      toTable: rel.REFERENCED_TABLE_NAME,
      toColumn: rel.REFERENCED_COLUMN_NAME,
      constraintName: rel.CONSTRAINT_NAME
    }));
  }

  generateSchemaDescription() {
    if (!this.schema) {
      throw new Error('Schema not discovered yet. Call discoverSchema() first.');
    }

    let description = `# هيكل قاعدة البيانات "${this.schema.database}"\n\n`;

    // وصف الجداول
    for (const [tableName, tableInfo] of Object.entries(this.schema.tables)) {
      description += `## جدول ${tableName}\n`;

      for (const column of tableInfo.columns) {
        const keyInfo = column.key === 'PRI' ? ' (المفتاح الرئيسي)' :
                       column.key === 'UNI' ? ' (فريد)' : '';
        const nullableInfo = column.nullable ? ' (يمكن أن يكون فارغ)' : ' (مطلوب)';

        description += `- ${column.name}: ${column.type}${keyInfo}${nullableInfo}`;
        if (column.comment) {
          description += ` - ${column.comment}`;
        }
        description += '\n';
      }
      description += '\n';
    }

    // وصف العلاقات
    if (this.relationships.length > 0) {
      description += '## العلاقات بين الجداول\n';
      for (const rel of this.relationships) {
        description += `- ${rel.fromTable}.${rel.fromColumn} -> ${rel.toTable}.${rel.toColumn}\n`;
      }
    }

    return description;
  }
}

// متغير عالمي لتخزين مكتشف قاعدة البيانات
let databaseDiscovery = null;

// دالة لتهيئة مكتشف قاعدة البيانات
export async function initializeDatabaseDiscovery(connectionConfig) {
  databaseDiscovery = new DatabaseSchemaDiscovery(connectionConfig);
  await databaseDiscovery.discoverSchema();
  return databaseDiscovery;
}

// دالة للحصول على وصف قاعدة البيانات
export function getDatabaseSchema() {
  if (!databaseDiscovery) {
    throw new Error('Database discovery not initialized. Call initializeDatabaseDiscovery() first.');
  }
  return databaseDiscovery.generateSchemaDescription();
}

/**
 * دالة لتحويل سؤال بالعربية إلى استعلام SQL
 * @param {string} question - السؤال بالعربية
 * @returns {Promise<string>} - استعلام SQL
 */
export async function translateQuestionToSQL(question, context = null) {
  const geminiModel = getGeminiModel();

  // استيراد سياق المحادثة إذا كان متاحاً
  let contextInfo = '';
  if (context && context.currentCustomer) {
    contextInfo = `
    معلومات السياق الحالي:
    - العميل الحالي: ${JSON.stringify(context.currentCustomer)}
    `;
  }

  // الحصول على وصف قاعدة البيانات الديناميكي
  const databaseSchema = getDatabaseSchema();

  // استخدام الفهرسة الذكية للحصول على معلومات إضافية
  let intelligentContext = '';

  try {
    // استيراد خدمة الفهرسة الذكية
    const { intelligentIndexing } = await import('../services/intelligent-indexing.js');

    // البحث عن الجداول ذات الصلة
    const relevantTables = await intelligentIndexing.findRelevantTables(question);

    // البحث عن أنماط استعلامات مشابهة
    const similarPatterns = await intelligentIndexing.findSimilarQueryPatterns(question);

    // البحث عن أمثلة ناجحة مشابهة
    const successfulExamples = await intelligentIndexing.findSimilarSuccessfulExamples(question);

    // تكوين السياق الذكي
    if (relevantTables.length > 0) {
      intelligentContext += `\n## الجداول ذات الصلة بالسؤال:\n`;
      relevantTables.slice(0, 3).forEach(table => {
        intelligentContext += `- ${table.table} (فئة: ${table.category})\n`;
      });
    }

    if (similarPatterns.length > 0) {
      intelligentContext += `\n## أنماط استعلامات مشابهة:\n`;
      similarPatterns.slice(0, 2).forEach(pattern => {
        intelligentContext += `- ${pattern.pattern}: ${pattern.sqlTemplate}\n`;
      });
    }

    if (successfulExamples.length > 0) {
      intelligentContext += `\n## أمثلة ناجحة مشابهة:\n`;
      successfulExamples.slice(0, 2).forEach(example => {
        intelligentContext += `- ${example.example}\n`;
      });
    }

  } catch (error) {
    console.warn("Could not use intelligent indexing:", error.message);
  }

  const prompt = `
  أنت مساعد ذكي متخصص في تحويل الأسئلة باللغة العربية إلى استعلامات SQL دقيقة.

  فيما يلي هيكل قاعدة البيانات التي نستخدمها:

  ${databaseSchema}

  السؤال هو: "${question}"

  ${contextInfo}

  ${intelligentContext}

  مهمتك هي:
  1. فهم السؤال باللغة العربية بدقة
  2. تحديد الجداول والحقول المطلوبة للإجابة على السؤال بشكل صحيح
  3. إنشاء استعلام SQL يجيب على السؤال بدقة تامة
  4. مراعاة العلاقات بين الجداول واستخدام JOIN عند الحاجة
  5. استخدام الدوال المناسبة مثل COUNT و SUM و AVG وغيرها حسب الحاجة
  6. ترتيب النتائج بشكل منطقي (ORDER BY) إذا كان ذلك مناسباً
  7. استخدام LIMIT عند الحاجة لتقييد عدد النتائج

  إرشادات مهمة جداً:
  - إذا كان السؤال عن "أعلى" أو "أكثر" أو "أفضل"، تأكد من استخدام ORDER BY مع DESC والحد المناسب
  - إذا كان السؤال عن فئة محددة مثل "الخضروات" أو "الفواكه"، تأكد من تضمين شرط WHERE category = 'الفئة المطلوبة' بالضبط
  - إذا كان السؤال يتعلق بالمبيعات، فاستخدم جداول الفواتير وتفاصيل الفواتير
  - إذا كان السؤال يتعلق بالمشتريات، فاستخدم جداول المشتريات وتفاصيل المشتريات
  - إذا كان السؤال يتعلق بحركة المخزون، فاستخدم جدول stock_movements مع JOIN على جدول items
  - تأكد من استخدام الفلاتر الصحيحة في WHERE لتطابق السؤال بدقة
  - إذا كان السؤال يطلب عدداً محدداً من النتائج (مثل "أعلى صنفين")، استخدم LIMIT بالعدد المطلوب
  - تأكد من أن الاستعلام يعمل بشكل صحيح ولا يحتوي على أخطاء نحوية

  ملاحظات هامة حول البيانات:
  - فئة "الخضروات" يجب أن تكون بالضبط 'خضروات' في شرط WHERE
  - فئة "الفواكه" يجب أن تكون بالضبط 'الفواكه' في شرط WHERE
  - تأكد من أن أسماء الجداول والحقول مكتوبة بشكل صحيح تماماً كما في هيكل قاعدة البيانات

  إرشادات خاصة بسياق المحادثة:
  - إذا كان السؤال يشير إلى عميل محدد بالاسم (مثل "خالد حسن")، تأكد من تضمين شرط WHERE customer_name = 'اسم العميل' بالضبط
  - إذا كان السؤال يتعلق بـ "المنتجات التي يشتريها" عميل معين، استخدم JOIN بين جداول customers و invoices و invoice_details و items
  - إذا كان السؤال متابعة لسؤال سابق عن عميل معين، استخدم معلومات السياق المقدمة لتحديد العميل المقصود

  أعطني فقط استعلام SQL بدون أي شرح إضافي أو علامات تنسيق مثل \`\`\`sql.
  `;

  try {
    const sqlQuery = await geminiModel.call(prompt);
    return sqlQuery.trim();
  } catch (error) {
    console.error('Error translating question to SQL:', error);
    // في حالة الخطأ، نعيد استعلام SQL افتراضي
    return 'SELECT * FROM items LIMIT 10';
  }
}

/**
 * دالة لإنشاء وصف للنتائج بناءً على السؤال والبيانات
 * @param {string} question - السؤال الأصلي
 * @param {Array} data - البيانات المسترجعة
 * @returns {Promise<string>} - وصف النتائج
 */
export async function generateResultDescription(question, data, sqlQuery, context = null) {
  const geminiModel = getGeminiModel();

  // استيراد سياق المحادثة إذا كان متاحاً
  let contextInfo = '';
  if (context && context.currentCustomer) {
    contextInfo = `
    معلومات السياق الحالي:
    - العميل الحالي: ${JSON.stringify(context.currentCustomer)}
    `;
  }

  const prompt = `
  أنت مساعد ذكي متخصص في تحليل البيانات وتقديم الإجابات الدقيقة باللغة العربية.

  السؤال الأصلي هو: "${question}"

  استعلام SQL المستخدم: ${sqlQuery}

  ${contextInfo}

  البيانات التي تم استرجاعها هي:
  ${JSON.stringify(data, null, 2)}

  مهمتك:
  1. تحليل البيانات المسترجعة بدقة
  2. تقديم إجابة مباشرة وواضحة للسؤال الأصلي
  3. ذكر أي أرقام أو إحصائيات مهمة ظهرت في النتائج
  4. إذا كان السؤال عن "أعلى" أو "أكثر"، حدد بوضوح ما هي العناصر الأعلى
  5. إذا كان السؤال عن فئة محددة (مثل الخضروات أو الفواكه)، تأكد من أن إجابتك تتعلق بهذه الفئة تحديداً
  6. إذا كان السؤال متابعة لسؤال سابق عن عميل معين، تأكد من ذكر اسم العميل في الإجابة

  قم بإنشاء وصف موجز (2-4 جمل) لهذه البيانات باللغة العربية. اشرح ما تظهره البيانات وكيف تجيب على السؤال الأصلي بدقة.

  إذا كانت البيانات لا تجيب مباشرة على السؤال، أشر إلى ذلك بوضوح.
  `;

  try {
    const description = await geminiModel.call(prompt);
    return description.trim();
  } catch (error) {
    console.error('Error generating result description:', error);
    return 'تم استرجاع البيانات بنجاح.';
  }
}
