import 'dotenv/config';
import express from 'express';
import cors from 'cors';
import bodyParser from 'body-parser';
import queryRoute from './routes/query.route.js';
import analyzeRoute from './routes/analyze.route.js';
import databaseRoute from './routes/database.route.js';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import { getAvailableModels } from './agent/ollama-model.js';

// التحقق من تحميل متغيرات البيئة
console.log('Environment variables loaded:');
console.log('- GOOGLE_API_KEY:', process.env.GOOGLE_API_KEY ? 'موجود' : 'غير موجود');
console.log('- MYSQL_HOST:', process.env.MYSQL_HOST || 'localhost (افتراضي)');
console.log('- MYSQL_DATABASE:', process.env.MYSQL_DATABASE || 'inventorymanagement (افتراضي)');

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();
app.use(cors());
app.use(bodyParser.json());

app.use('/api/query', queryRoute);
app.use('/api/analyze', analyzeRoute);
app.use('/api/database', databaseRoute);

// تقديم ملفات الواجهة الأمامية من مجلد frontend
app.use(express.static(path.join(__dirname, '..', 'frontend')));

app.get('/', (_, res) => {
  res.send('AI Data Agent backend is running.');
});

// يجب أن يكون هذا قبل app.use(express.static(...))
app.get('/api/models', (_, res) => {
  const models = getAvailableModels();
  res.json(models);
});

const PORT = process.env.PORT || 3003;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});