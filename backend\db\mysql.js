import { DataSource } from "typeorm";
import { SqlDatabase } from "langchain/sql_db";

let dbInstance = null;

async function getDatabase() {
  if (dbInstance) return dbInstance;
  try {
    console.log("Creating TypeORM DataSource...");
    console.log("Database config:", {
      host: process.env.MYSQL_HOST || "localhost",
      user: process.env.MYSQL_USER || "root",
      database: process.env.MYSQL_DATABASE || "inventorymanagement"
    });

    // إنشاء كائن DataSource لـ TypeORM
    const dataSource = new DataSource({
      type: "mysql",
      host: process.env.MYSQL_HOST || "localhost",
      port: 3306,
      username: process.env.MYSQL_USER || "root",
      password: process.env.MYSQL_PASSWORD || "",
      database: process.env.MYSQL_DATABASE || "inventorymanagement",
      synchronize: false,
    });

    // تهيئة اتصال قاعدة البيانات
    await dataSource.initialize();
    console.log("DataSource initialized successfully");

    // إنشاء كائن SqlDatabase باستخدام DataSource
    dbInstance = new SqlDatabase({
      appDataSource: dataSource,
      includesTables: ["products"]
    });

    console.log("SqlDatabase instance created successfully");
    return dbInstance;
  } catch (error) {
    console.error("Error connecting to MySQL database:", error);
    throw error;
  }
}

export { getDatabase };
